package models

import (
	"cx/app/utils"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"

	. "cx/database"
)

type LevelArray []int

// Value 實現 driver.Valuer 接口
func (la LevelArray) Value() (driver.Value, error) {
	return json.Marshal(la)
}

// Scan 實現 sql.Scanner 接口
func (la *LevelArray) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, &la)
}

type Activity struct {
	ID      uint      `form:"id" json:"id"`
	Name    string    `form:"name" json:"name"`
	AType   string    `form:"a_type" json:"a_type"` // 適用類型: P:購買課程，S:儲值課費，G:購買道具卡
	StartAt null.Time `form:"start_at" json:"start_at"`
	EndAt   null.Time `form:"end_at" json:"end_at"`

	TriggerNumLimit string `form:"trigger_num_limit" json:"trigger_num_limit"` // 觸發次數限制，Y:限制，N:不限制
	TriggerNum      int    `form:"trigger_num" json:"trigger_num"`             // 觸發次數

	RebateType   string        `form:"rebate_type" json:"rebate_type"` //	返點類型: F:固定，L:依等級
	Rebate       *int          `form:"rebate" json:"rebate"`
	RebateLevels []RebateLevel `gorm:"<-:false;foreignKey:ActivityID;references:ID" form:"rebate_levels" json:"rebate_levels"`

	FreeProLimit string   `form:"free_pro_limit" json:"free_pro_limit"` // 免費課程限制，Y:設定，N:不設定，僅限儲值課費
	FreeProNum   int      `form:"free_pro_num" json:"free_pro_num"`     // 免費課程之次數，僅限儲值課費
	FreeProID    null.Int `form:"free_pro_id" json:"free_pro_id"`       // 免費課程之ID，僅限儲值課費

	FreeCounselingLimit bool                     `form:"free_counseling_limit" json:"free_counseling_limit"`
	FreeCounselings     []ActivityFreeCounseling `gorm:"<-:false;foreignKey:ActivityID;references:ID" form:"free_counselings" json:"free_counselings"`

	FreeGoodsLimit string   `form:"free_goods_limit" json:"free_goods_limit"` // 免費道具卡限制，Y:設定，N:不設定
	FreeGoodsNum   int      `form:"free_goods_num" json:"free_goods_num"`     // 免費道具卡之次數
	FreeGoodsID    null.Int `form:"free_goods_id" json:"free_goods_id"`       // 免費道具卡之ID

	Levels LevelArray `form:"levels" json:"levels"`

	AProKind  string     `form:"a_pro_kind" json:"a_pro_kind"` // 適用課程類型: Y:限定，N:不限定
	AProKinds []AProKind `gorm:"<-:false;foreignKey:ActivityID;references:ID" form:"a_pro_kinds" json:"a_pro_kinds"`

	APro  string `form:"a_pro" json:"a_pro"` // 適用課程: Y:限定，N:不限定
	APros []APro `gorm:"<-:false;foreignKey:ActivityID;references:ID" form:"a_pros" json:"a_pros"`

	AProPriceLimit string `form:"a_pro_price_limit" json:"a_pro_price_limit"` // 適用課程價格限制，Y:限定，N:不限定，僅限購買課程
	AProPrice      int    `form:"a_pro_price" json:"a_pro_price"`             // 需要下單課程之價格

	ProNumLimit string `form:"pro_num_limit" json:"pro_num_limit"` // 課程下單限制，Y:限制，N:不限制，僅限購買課程
	ProNum      int    `form:"pro_num" json:"pro_num"`             // 需要下單課程之數量

	ProLimit     string          `form:"pro_limit" json:"pro_limit"`         // 課程限制，A:全部上過，O:其中X堂，N:不限制，僅限購買課程
	ProLimitNum  int             `form:"pro_limit_num" json:"pro_limit_num"` // 課程限制之堂數
	LimitPros    []LimitPro      `gorm:"<-:false;foreignKey:ActivityID;references:ID" form:"limit_pros" json:"limit_pros"`
	MemLimitPros []MemberProduct `gorm:"<-:false;many2many:member_products" form:"mem_limit_pros" json:"mem_limit_pros"`

	APayTypes   []APayType `gorm:"<-:false;foreignKey:ActivityID;references:ID" form:"a_pay_types" json:"a_pay_types"` // 適用付款方式: atm:ATM轉帳，newebpay:線上刷卡，zingala:無卡分期
	AStoreLimit string     `form:"a_store_limit" json:"a_store_limit"`                                                 // 適用儲值點數限制，Y:限定，N:不限定
	AStores     []AStore   `gorm:"<-:false;foreignKey:ActivityID;references:ID" form:"a_stores" json:"a_stores"`       // 適用儲值點數

	GoodsNumLimit string   `form:"goods_num_limit" json:"goods_num_limit"`                                     // 道具卡下單數量限制，Y:限制，N:不限制
	GoodsNum      int      `form:"goods_num" json:"goods_num"`                                                 // 道具卡下單數量
	AGoodsLimit   string   `form:"a_goods_limit" json:"a_goods_limit"`                                         // 適用道具卡限制，Y:限定，N:不限定
	AGoods        []AGoods `gorm:"<-:false;foreignKey:ActivityID;references:ID" form:"a_goods" json:"a_goods"` // 適用道具卡

	Note        string         `form:"note" json:"note"`
	Status      string         `form:"status" json:"status"`
	CreatedByID uint           `gorm:"<-:create" form:"created_by_id" json:"created_by_id"`
	CreatedBy   string         `gorm:"-" form:"created_by" json:"created_by"`
	UpdatedByID uint           `form:"updated_by_id" json:"updated_by_id"`
	UpdatedBy   string         `gorm:"-" form:"updated_by" json:"updated_by"`
	CreatedAt   time.Time      `gorm:"<-:create" form:"created_at" json:"created_at"`
	UpdatedAt   time.Time      `form:"updated_at" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"<-:false" form:"deleted_at" json:"deleted_at"`
}

func (Activity) TableName() string {
	return "activities"
}

type ActivityFreeCounseling struct {
	ActivityID    uint `gorm:"<-:false" form:"activity_id" json:"activity_id"`
	CounselTypeID uint `gorm:"<-:false" form:"counsel_type_id" json:"counsel_type_id"`
	Amount        int  `gorm:"<-:false" form:"amount" json:"amount"`
}

func (ActivityFreeCounseling) TableName() string {
	return "activity_free_counselings"
}

type ActivityHistory struct {
	ID            uint           `form:"id" json:"id"`
	MemberID      uint           `form:"member_id" json:"member_id"`
	ActivityID    uint           `form:"activity_id" json:"activity_id"`
	ProOrderID    null.Int       `form:"pro_order_id" json:"pro_order_id"`       // 購買課程訂單ID
	StoredOrderID null.Int       `form:"stored_order_id" json:"stored_order_id"` // 儲值課費訂單ID
	GoodsOrderID  null.Int       `form:"goods_order_id" json:"goods_order_id"`   // 道具卡訂單ID
	CreatedAt     time.Time      `gorm:"<-:create" form:"created_at" json:"created_at"`
	UpdatedAt     time.Time      `form:"updated_at" json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"<-:false" form:"deleted_at" json:"deleted_at"`
}

type ActivityOrderID struct {
	ProOrderID   uint // 購買課程訂單ID
	StoreOrderID uint // 儲值課費訂單ID
	GoodsOrderID uint // 道具卡訂單ID
}

func (a *Activity) UpdateAssociations(db *gorm.DB) error {
	if a.RebateType == "L" {
		if err := a.UpdateRebateLevels(db); err != nil {
			return err
		}
	}

	if a.AType == "P" {
		if a.AProKind == "Y" {
			if err := a.UpdateAProKinds(db); err != nil {
				return err
			}
		}

		if a.APro == "Y" {
			if err := a.UpdateAPros(db); err != nil {
				return err
			}
		}

		if a.ProLimit == "O" || a.ProLimit == "A" {
			if err := a.UpdateLimitPros(db); err != nil {
				return err
			}
		}
	} else if a.AType == "S" {
		if err := a.UpdateAPayTypes(db); err != nil {
			return err
		}
	} else if a.AType == "G" {
		if err := a.UpdateAGoods(db); err != nil {
			return err
		}
	}

	if a.FreeCounselingLimit {
		if err := a.UpdateFreeCounselings(db); err != nil {
			return err
		}
	}

	if err := a.UpdateAStores(db); err != nil {
		return err
	}

	return nil
}

type RebateLevel struct {
	ActivityID uint `form:"activity_id" json:"activity_id"`
	Level      uint `form:"level" json:"level"`
	Rebate     int  `form:"rebate" json:"rebate"`
}

func (RebateLevel) TableName() string {
	return "activity_rebate_levels"
}

func (a *Activity) UpdateRebateLevels(db *gorm.DB) error {
	if err := db.Where("activity_id = ?", a.ID).Delete(&RebateLevel{}).Error; err != nil {
		return err
	}

	if len(a.RebateLevels) == 0 {
		return nil
	}

	for i := range a.RebateLevels {
		a.RebateLevels[i].ActivityID = a.ID
	}

	if err := db.Create(&a.RebateLevels).Error; err != nil {
		return err
	}

	return nil
}

type AProKind struct {
	ActivityID uint `form:"activity_id" json:"activity_id"`
	ProKindID  uint `form:"kind_id" json:"kind_id"`
}

func (AProKind) TableName() string {
	return "activity_pro_kinds"
}

func (a *Activity) GetAProKindList() []uint {
	kindIDs := []uint{}

	for _, k := range a.AProKinds {
		kindIDs = append(kindIDs, k.ProKindID)
	}

	return kindIDs
}

func (a *Activity) UpdateAProKinds(db *gorm.DB) error {
	if err := db.Where("activity_id = ?", a.ID).Delete(&AProKind{}).Error; err != nil {
		return err
	}

	if len(a.AProKinds) == 0 {
		return nil
	}

	for i := range a.AProKinds {
		a.AProKinds[i].ActivityID = a.ID
	}

	if err := db.Create(&a.AProKinds).Error; err != nil {
		return err
	}

	return nil
}

type APro struct {
	ActivityID uint `form:"activity_id" json:"activity_id"`
	ProID      uint `form:"pro_id" json:"pro_id"`
}

func (APro) TableName() string {
	return "activity_pros"
}

func (a *Activity) GetAProList() []uint {
	proIDs := []uint{}

	for _, p := range a.APros {
		proIDs = append(proIDs, p.ProID)
	}

	return proIDs
}

func (a *Activity) UpdateAPros(db *gorm.DB) error {
	if err := db.Where("activity_id = ?", a.ID).Delete(&APro{}).Error; err != nil {
		return err
	}

	if len(a.APros) == 0 {
		return nil
	}

	for i := range a.APros {
		a.APros[i].ActivityID = a.ID
	}

	if err := db.Create(&a.APros).Error; err != nil {
		return err
	}

	return nil
}

type LimitPro struct {
	ActivityID uint `form:"activity_id" json:"activity_id"`
	ProID      uint `form:"pro_id" json:"pro_id"`
}

func (LimitPro) TableName() string {
	return "activity_limit_pros"
}

func (a *Activity) UpdateLimitPros(db *gorm.DB) error {
	if err := db.Where("activity_id = ?", a.ID).Delete(&LimitPro{}).Error; err != nil {
		return err
	}

	if len(a.LimitPros) == 0 {
		return nil
	}

	for i := range a.LimitPros {
		a.LimitPros[i].ActivityID = a.ID
	}

	if err := db.Create(&a.LimitPros).Error; err != nil {
		return err
	}

	return nil
}

func (a *Activity) UpdateFreeCounselings(db *gorm.DB) error {
	if err := db.Where("activity_id = ?", a.ID).Delete(&ActivityFreeCounseling{}).Error; err != nil {
		return err
	}

	if len(a.FreeCounselings) == 0 {
		return nil
	}

	for i := range a.FreeCounselings {
		a.FreeCounselings[i].ActivityID = a.ID
	}

	if err := db.Create(&a.FreeCounselings).Error; err != nil {
		return err
	}

	return nil
}

type APayType struct {
	ActivityID uint   `form:"activity_id" json:"activity_id"`
	PayType    string `form:"pay_type" json:"pay_type"`
}

func (APayType) TableName() string {
	return "activity_pay_types"
}

func (a *Activity) UpdateAPayTypes(db *gorm.DB) error {
	if err := db.Where("activity_id = ?", a.ID).Delete(&APayType{}).Error; err != nil {
		return err
	}

	if len(a.APayTypes) == 0 {
		return nil
	}

	for i := range a.APayTypes {
		a.APayTypes[i].ActivityID = a.ID
	}

	if err := db.Create(&a.APayTypes).Error; err != nil {
		return err
	}

	return nil
}

type AStore struct {
	ActivityID uint `form:"activity_id" json:"activity_id"`
	StoreID    uint `form:"store_id" json:"store_id"`
}

func (AStore) TableName() string {
	return "activity_stores"
}

func (a *Activity) UpdateAStores(db *gorm.DB) error {
	if err := db.Where("activity_id = ?", a.ID).Delete(&AStore{}).Error; err != nil {
		return err
	}

	if len(a.AStores) == 0 {
		return nil
	}

	for i := range a.AStores {
		a.AStores[i].ActivityID = a.ID
	}

	if err := db.Create(&a.AStores).Error; err != nil {
		return err
	}

	return nil
}

type AGoods struct {
	ActivityID uint `form:"activity_id" json:"activity_id"`
	GoodsID    uint `form:"goods_id" json:"goods_id"`
}

func (AGoods) TableName() string {
	return "activity_goods"
}

func (a *Activity) UpdateAGoods(db *gorm.DB) error {
	if err := db.Where("activity_id = ?", a.ID).Delete(&AGoods{}).Error; err != nil {
		return err
	}

	if len(a.AGoods) == 0 {
		return nil
	}

	for i := range a.AGoods {
		a.AGoods[i].ActivityID = a.ID
	}

	if err := db.Create(&a.AGoods).Error; err != nil {
		return err
	}

	return nil
}

// 檢查購買課程是否符合活動條件
func CheckProActivityQualify(conn *gorm.DB, mem *Member, pro *Product, memPt *Point, proOrderID uint) error {
	activity := []Activity{}
	now := time.Now()

	if err := conn.Preload("RebateLevels", "level = ?", mem.Level).Preload("AProKinds").Preload("APros").Preload("LimitPros").
		Preload("FreeCounselings").
		Preload("AStores").
		Where("a_type = 'P'").
		Where("start_at IS NULL OR start_at <= ?", now).
		Where("end_at IS NULL OR end_at > ?", now).
		Where("status = 'Y'").Find(&activity).Error; err != nil {
		return err
	}

	for _, act := range activity {
		var (
			isTriggerNumMatch bool // 確認觸發次數
			isLevelMatch      bool // 確認學員適用等級
			isStoreMatch      bool // 確認儲值點數
			isProMatch        bool // 確認是否包含在適用類別或課程中
			isProPriceMatch   bool // 確認課程價格
			isProNumMatch     bool // 確認課程下單數量
			isLimitProMatch   bool // 確認是否上過指定課程

			triggerNum int
		)

		// 確認觸發次數
		isTriggerNumMatch, triggerNum = checkTriggerNum(conn, mem, &act)
		if !isTriggerNumMatch {
			continue
		}

		// 確認level
		for _, level := range act.Levels {
			if mem.Level == level {
				isLevelMatch = true
				break
			}
		}

		if !isLevelMatch {
			continue
		}

		// 確認儲值點數
		if act.AStoreLimit == "N" {
			isStoreMatch = true
		} else {
			db := ConnectDB()
			defer CloseDB(db)

			storeIDs := make([]uint, 0)

			if err := db.Model(&Order{}).Select("stored_id").
				Where("member_id = ?", mem.ID).
				Where("pay_status = 'Y'").
				Where("created_at >= ? OR ?", act.StartAt.Time, act.StartAt.IsZero()).
				Where("created_at < ? OR ?", act.EndAt.Time, act.EndAt.IsZero()).
				Scan(&storeIDs).Error; err != nil {
				return err
			}

			if len(storeIDs) == 0 {
				continue
			}

			for _, s := range act.AStores {
				for _, storeID := range storeIDs {
					if s.StoreID == storeID {
						isStoreMatch = true
						break
					}
				}
				if isStoreMatch {
					break
				}
			}

			if !isStoreMatch {
				continue
			}
		}

		// 確認課程
		if act.APro == "N" && act.AProKind == "N" {
			isProMatch = true
		} else {
			if act.AProKind == "Y" {
				for _, k := range act.AProKinds {
					if k.ProKindID == pro.ProKindID {
						isProMatch = true
						break
					}
				}
			}

			if !isProMatch && act.APro == "Y" {
				for _, p := range act.APros {
					if p.ProID == pro.ID {
						isProMatch = true
						break
					}
				}
			}

			if !isProMatch {
				continue
			}
		}

		if act.AProPriceLimit == "N" {
			isProPriceMatch = true
		} else if pro.ProPoints >= act.AProPrice {
			isProPriceMatch = true
		} else {
			continue
		}

		// 確認課程下單數量
		if act.ProNumLimit == "N" {
			isProNumMatch = true
		} else {
			db := ConnectDB()
			defer CloseDB(db)

			if act.AProKind == "Y" && len(act.AProKinds) > 0 {
				db = db.Or("pro.pro_kind_id IN (?)", act.GetAProKindList())
			}

			if act.APro == "Y" && len(act.APros) > 0 {
				db = db.Or("pro.id IN (?)", act.GetAProList())
			}

			if act.AProPriceLimit == "Y" {
				db = db.Where("product_orders.points >= ?", act.AProPrice)
			}

			db = db.Model(&ProductOrder{}).Select("DISTINCT(product_orders.id)").
				Joins("JOIN products AS pro ON pro.id = product_orders.product_id AND pro.deleted_at IS NULL").
				Where("product_orders.member_id = ?", mem.ID).
				Where("product_orders.created_at >= ? OR ?", act.StartAt.Time, act.StartAt.IsZero()).
				Where("product_orders.created_at < ? OR ?", act.EndAt.Time, act.EndAt.IsZero())

			memPros := []MemberProduct{}
			if err := db.Find(&memPros).Error; err != nil {
				return err
			}

			if (len(memPros) + 1 - triggerNum*act.ProNum) >= act.ProNum {
				isProNumMatch = true
			}

			if !isProNumMatch {
				continue
			}
		}

		// 上過的課程
		if act.ProLimit == "N" {
			isLimitProMatch = true
		} else {
			var limitPros []uint
			memLimitPros := []MemberProduct{}

			for _, p := range act.LimitPros {
				limitPros = append(limitPros, p.ProID)
			}

			if err := conn.Select("DISTINCT(`product_id`)").Where("member_id = ? AND product_id IN ?", mem.ID, limitPros).
				Find(&memLimitPros).Error; err != nil {
				return err
			}

			if act.ProLimit == "A" && len(memLimitPros) >= len(limitPros) {
				isLimitProMatch = true
			}

			if act.ProLimit == "O" && len(memLimitPros) > 0 {
				isLimitProMatch = true
			}

			if !isLimitProMatch {
				continue
			}
		}

		// 檢查是否符合活動條件
		if isTriggerNumMatch && isLevelMatch && isStoreMatch && isProMatch && isProNumMatch && isProPriceMatch && isLimitProMatch {
			var (
				rebate int
				level  string
			)

			if err := conn.Create(&ActivityHistory{
				MemberID:   mem.ID,
				ActivityID: act.ID,
				ProOrderID: null.IntFrom(int64(proOrderID)),
			}).Error; err != nil {
				return err
			}

			// 返點
			if act.RebateType == "F" {
				rebate = *act.Rebate
			} else if act.RebateType == "L" {
				if len(act.RebateLevels) > 0 {
					rebate = act.RebateLevels[0].Rebate
				}
				level = MemberLevel[int(mem.Level)]
			}

			if rebate > 0 {
				if err := conn.Model(&memPt).Update("points", gorm.Expr("points + ?", rebate)).Error; err != nil {
					return err
				}

				if err := conn.Create(&PointHistory{
					MemberID:   mem.ID,
					ActivityID: null.IntFrom(int64(act.ID)),
					Points:     rebate,
					Reason:     fmt.Sprintf("%s｜%s下單%s｜返點$%d", utils.RemoveBreakString(act.Name), level, utils.RemoveBreakString(pro.ProName), rebate),
				}).Error; err != nil {
					return err
				}
			}

			// 贈送輔導
			if err := giveCounselingByActivity(conn, mem, &act); err != nil {
				return err
			}

			// 贈送道具卡
			if err := giveGoodsByActivity(conn, mem, &act); err != nil {
				return err
			}
		}
	}

	return nil
}

// 檢查儲值課費是否符合活動條件
func CheckOrderActivityQualify(conn *gorm.DB, mem *Member, orderID uint) error {
	order := Order{}
	activities := []Activity{}
	if err := conn.First(&order, orderID).Error; err != nil {
		return err
	}

	orderAt := order.CreatedAt
	storeID := uint(order.StoredID.Int64)

	if err := conn.Preload("RebateLevels", "level = ?", mem.Level).Preload("FreeCounselings").
		Preload("APayTypes", "pay_type = ?", order.PayMethod).Preload("AStores", "store_id = ?", storeID).
		Where("a_type = 'S'").
		Where("start_at IS NULL OR start_at <= ?", orderAt).
		Where("end_at IS NULL OR end_at > ?", orderAt).
		Where("status = 'Y'").Find(&activities).Error; err != nil {
		return err
	}

	for _, act := range activities {
		var (
			isTriggerNumMatch bool
			isLevelMatch      bool
			isPayTypeMatch    bool
			isStoreMatch      bool
		)

		// 確認觸發次數
		if isMatch, _ := checkTriggerNum(conn, mem, &act); isMatch {
			isTriggerNumMatch = true
		} else {
			fmt.Println("觸發次數不符合")
			continue
		}

		// 確認level
		for _, level := range act.Levels {
			if mem.Level == level {
				isLevelMatch = true
				break
			}
		}

		if !isLevelMatch {
			fmt.Println("等級不符合")
			continue
		}

		// 確認付款方式
		if len(act.APayTypes) == 0 {
			fmt.Println("付款方式不符合")
			continue
		} else {
			for _, p := range act.APayTypes {
				if p.PayType == order.PayMethod {
					isPayTypeMatch = true
					break
				}
			}

			if !isPayTypeMatch {
				fmt.Println("付款方式不符合")
				continue
			}
		}

		// 確認儲值
		if act.AStoreLimit == "N" {
			isStoreMatch = true
		} else {
			for _, s := range act.AStores {
				if s.StoreID == storeID {
					isStoreMatch = true
					break
				}
			}

			if !isStoreMatch {
				fmt.Println("StoreID:", storeID)
				fmt.Println("儲值不符合")
				continue
			}
		}

		// 檢查是否符合活動條件
		if isTriggerNumMatch && isLevelMatch && isPayTypeMatch && isStoreMatch {
			var (
				rebate int
				level  string
			)

			if err := conn.Create(&ActivityHistory{
				MemberID:      mem.ID,
				ActivityID:    act.ID,
				StoredOrderID: null.IntFrom(int64(order.ID)),
			}).Error; err != nil {
				return err
			}

			// 返點
			if act.RebateType == "F" {
				rebate = *act.Rebate
			} else if act.RebateType == "L" {
				if len(act.RebateLevels) > 0 {
					rebate = act.RebateLevels[0].Rebate
				}

				level = MemberLevel[int(mem.Level)]
			}

			// 返點
			fmt.Println("活動名稱:", act.Name)
			fmt.Println("返點:", rebate)
			if rebate > 0 {

				if err := conn.Model(&Point{}).Where("member_id = ?", mem.ID).Update("points", gorm.Expr("points + ?", rebate)).Error; err != nil {
					return err
				}

				if err := conn.Create(&PointHistory{
					MemberID:   mem.ID,
					ActivityID: null.IntFrom(int64(act.ID)),
					Points:     rebate,
					Reason:     fmt.Sprintf("%s｜%s儲值$%d｜返點$%d", utils.RemoveBreakString(act.Name), level, order.Points, rebate),
				}).Error; err != nil {
					return err
				}
			}

			// 贈送課程
			if act.FreeProLimit == "Y" && act.FreeProNum > 0 {
				freePro := Product{}

				if err := conn.Where("id = ?", act.FreeProID).First(&freePro).Error; err != nil {
					continue
				}

				// 取得課程天數
				proOpenDays := 7
				if freePro.ProOpenType == "L" {
					// 依等級給天數
					if err := conn.Model(&ProOpenLevel{}).Select("open_days").
						Where("pro_id = ? AND level = ?", freePro.ID, mem.Level).
						Scan(&proOpenDays).Error; err != nil {
						return err
					}
				} else {
					proOpenDays = freePro.ProStatusOpen
				}

				// 新增會員課程
				for i := 0; i < act.FreeProNum; i++ {
					memPro := MemberProduct{
						MemberID:          mem.ID,
						ProductID:         freePro.ID,
						Points:            0,
						AllowCounseling:   freePro.AllowCounseling,
						CounselDeadlineAt: freePro.GetCounselDeadlineAt(),
						IsReview:          "N",
						IsRelive:          "N",
						Status:            "Y",
						ExpiredAt:         time.Now().Add(time.Duration(proOpenDays) * 24 * time.Hour),
					}

					if err := conn.Create(&memPro).Error; err != nil {
						return err
					}

					// 作業
					if err := InitMemWorks(conn, &memPro); err != nil {
						return err
					}
				}

				// 新增課程紀錄
				if err := conn.Create(&PointHistory{
					MemberID:   mem.ID,
					ActivityID: null.IntFrom(int64(act.ID)),
					Points:     0,
					Reason:     fmt.Sprintf("%s｜%s儲值$%d｜贈送課程 - %s x %d", utils.RemoveBreakString(act.Name), level, order.Points, utils.RemoveBreakString(freePro.ProName), act.FreeProNum),
				}).Error; err != nil {
					return err
				}
			}

			// 贈送輔導
			if err := giveCounselingByActivity(conn, mem, &act); err != nil {
				return err
			}

			// 贈送道具卡
			if err := giveGoodsByActivity(conn, mem, &act); err != nil {
				return err
			}
		}
	}

	return nil
}

// 檢查購買道具卡是否符合活動條件
func CheckGoodsActivityQualify(conn *gorm.DB, mem *Member, goods *Goods, goodsOrderID uint) error {
	var activities []Activity

	if err := conn.Preload("RebateLevels", "level = ?", mem.Level).
		Preload("FreeCounselings").
		Preload("AGoods", "goods_id = ?", goods.ID).
		Where("a_type = 'G'").
		Where("start_at IS NULL OR start_at <= ?", time.Now()).
		Where("end_at IS NULL OR end_at > ?", time.Now()).
		Where("status = 'Y'").
		Find(&activities).Error; err != nil {
		return err
	}

	for _, act := range activities {
		var (
			isGoodsMatch      bool // 確認適用道具卡
			isLevelMatch      bool // 確認適用等級
			isTriggerNumMatch bool // 確認觸發次數
			isGoodsNumMatch   bool // 確認道具卡下單數量
		)

		// 確認道具卡
		if act.AGoodsLimit == "N" {
			isGoodsMatch = true
		} else if len(act.AGoods) > 0 {
			isGoodsMatch = true
		} else {
			continue
		}

		// 確認觸發次數
		isMatch, totalTrigger, remainingTriggerNum := checkGoodsTriggerNum(conn, &act, mem.ID, goods.ID)
		if isMatch {
			isTriggerNumMatch = true
		} else {
			continue
		}

		// 確認適用level
		for _, level := range act.Levels {
			if mem.Level == level {
				isLevelMatch = true
				break
			}
		}

		if !isLevelMatch {
			continue
		}

		// 確認下單數量
		canTriggerNum := 0 // 可觸發次數
		if act.GoodsNumLimit == "N" {
			isGoodsNumMatch = true
		} else {
			var goodsOrders []GoodsOrder

			if err := conn.Select("id", "goods_num").
				Where("member_id = ?", mem.ID).
				Where("goods_id = ?", goods.ID).
				Where("goods_num > 0").
				Where("points > 0").
				Where("created_at >= ? OR ?", act.StartAt.Time, act.StartAt.IsZero()).
				Where("created_at < ? OR ?", act.EndAt.Time, act.EndAt.IsZero()).
				Find(&goodsOrders).Error; err != nil {
				return err
			}

			totalNum := 0
			for _, order := range goodsOrders {
				totalNum += order.GoodsNum
			}

			canTriggerNum = (totalNum - act.GoodsNum*totalTrigger) / act.GoodsNum

			if canTriggerNum > remainingTriggerNum {
				canTriggerNum = remainingTriggerNum
			}

			if canTriggerNum > 0 {
				isGoodsNumMatch = true
			} else {
				continue
			}
		}

		if isTriggerNumMatch && isLevelMatch && isGoodsMatch && isGoodsNumMatch {
			for i := 0; i < canTriggerNum; i++ {
				var (
					rebate int
					level  string
				)

				if err := conn.Create(&ActivityHistory{
					MemberID:     mem.ID,
					ActivityID:   act.ID,
					GoodsOrderID: null.IntFrom(int64(goodsOrderID)),
				}).Error; err != nil {
					return err
				}

				// 返點
				if act.RebateType == "F" {
					rebate = *act.Rebate
				} else if act.RebateType == "L" {
					if len(act.RebateLevels) > 0 {
						rebate = act.RebateLevels[0].Rebate
					}

					level = MemberLevel[int(mem.Level)]
				}

				// 返點
				if rebate > 0 {
					if err := conn.Model(&Point{}).Where("member_id = ?", mem.ID).Update("points", gorm.Expr("points + ?", rebate)).Error; err != nil {
						return err
					}

					if err := conn.Create(&PointHistory{
						MemberID:   mem.ID,
						ActivityID: null.IntFrom(int64(act.ID)),
						Points:     rebate,
						Reason:     fmt.Sprintf("%s｜%s購買道具卡 - %s｜返點$%d", utils.RemoveBreakString(act.Name), level, utils.RemoveBreakString(goods.Name), rebate),
					}).Error; err != nil {
						return err
					}
				}

				// 贈送輔導
				if err := giveCounselingByActivity(conn, mem, &act); err != nil {
					return err
				}

				// 贈送道具卡
				// if err := giveGoodsByActivity(conn, mem, &act); err != nil {
				// 	return err
				// }

				if act.FreeGoodsLimit == "Y" && act.FreeGoodsNum > 0 {
					// 新增會員道具卡
					var goodsExpiredAt null.Time
					if !goods.Days.IsZero() {
						goodsExpiredAt = null.TimeFrom(time.Now().AddDate(0, 0, int(goods.Days.Int64)))
					}

					for i := 0; i < act.FreeGoodsNum; i++ {
						memGoods := MemberGoods{
							MemberID:  mem.ID,
							GoodsID:   null.IntFrom(int64(goods.ID)),
							GoodsType: null.IntFrom(int64(goods.GoodsType)),
							ExpiredAt: goodsExpiredAt,
							Status:    "N",
							IsFree:    "Y",
						}

						if err := conn.Create(&memGoods).Error; err != nil {
							return err
						}
					}
					// 新增道具卡紀錄
					if err := conn.Create(&PointHistory{
						MemberID:   mem.ID,
						ActivityID: null.IntFrom(int64(act.ID)),
						Points:     0,
						Reason:     fmt.Sprintf("%s｜贈送道具卡 - %s x %d", utils.RemoveBreakString(act.Name), utils.RemoveBreakString(goods.Name), act.FreeGoodsNum),
					}).Error; err != nil {
						return err
					}
				}
			}
		}
	}

	return nil
}

// 檢查觸發次數
func checkTriggerNum(conn *gorm.DB, mem *Member, act *Activity) (bool, int) {
	var triggerNum int
	if err := conn.Model(&ActivityHistory{}).Select("COUNT(id)").
		Where("member_id = ? AND activity_id = ?", mem.ID, act.ID).
		Where("created_at >= ? OR ?", act.StartAt.Time, act.StartAt.IsZero()).
		Where("created_at < ? OR ?", act.EndAt.Time, act.EndAt.IsZero()).
		Scan(&triggerNum).Error; err != nil {
		return false, triggerNum
	}

	if act.TriggerNumLimit == "N" || triggerNum < act.TriggerNum {
		return true, triggerNum
	}

	return false, triggerNum
}

func checkGoodsTriggerNum(conn *gorm.DB, act *Activity, memberID uint, goodsID uint) (bool, int, int) {
	var (
		triggerNum int
	)
	remainingTriggerNum := 0

	if err := conn.Model(&ActivityHistory{}).Select("COUNT(activity_histories.id)").
		Joins("JOIN goods_orders ON goods_orders.id = activity_histories.goods_order_id AND goods_orders.goods_id = ?", goodsID).
		Where("activity_histories.member_id = ? AND activity_histories.activity_id = ?", memberID, act.ID).
		Where("activity_histories.created_at >= ? OR ?", act.StartAt.Time, act.StartAt.IsZero()).
		Where("activity_histories.created_at < ? OR ?", act.EndAt.Time, act.EndAt.IsZero()).
		Scan(&triggerNum).Error; err != nil {
		return false, triggerNum, remainingTriggerNum
	}

	if act.TriggerNumLimit == "N" {
		remainingTriggerNum = 999
		return true, triggerNum, remainingTriggerNum
	}

	if triggerNum < act.TriggerNum {
		remainingTriggerNum = act.TriggerNum - triggerNum
		return true, triggerNum, remainingTriggerNum
	}

	return false, triggerNum, remainingTriggerNum
}

// 贈送輔導
func giveCounselingByActivity(conn *gorm.DB, mem *Member, act *Activity) error {
	if !act.FreeCounselingLimit {
		return nil
	}

	for _, freeCounsel := range act.FreeCounselings {
		counselType := CounselType{}
		if err := conn.First(&counselType, freeCounsel.CounselTypeID).Error; err != nil {
			return err
		}

		for i := 0; i < freeCounsel.Amount; i++ {
			app := CounselAppointment{}

			if _, err := app.Create(conn, &CounselAppointmentCreateParams{
				MemberID:      mem.ID,
				CounselTypeID: freeCounsel.CounselTypeID,
				Price:         0,
				IsFree:        true,
			}); err != nil {
				return err
			}

			if err := CreateDefaultCounselWorks(conn, freeCounsel.CounselTypeID, app.ID, mem.ID); err != nil {
				return err
			}
		}

		if err := conn.Create(&PointHistory{
			MemberID:   mem.ID,
			ActivityID: null.IntFrom(int64(act.ID)),
			Reason:     fmt.Sprintf("%s｜贈送諮詢輔導 - %s x %d", utils.RemoveBreakString(act.Name), utils.RemoveBreakString(counselType.Name), freeCounsel.Amount),
		}).Error; err != nil {
			return err
		}
	}

	return nil
}

// 贈送道具卡
func giveGoodsByActivity(conn *gorm.DB, mem *Member, act *Activity) error {
	if act.FreeGoodsLimit == "Y" && act.FreeGoodsNum > 0 {
		goods := Goods{}

		if err := conn.Where("id = ?", act.FreeGoodsID).First(&goods).Error; err != nil {
			return err
		}

		var expiredAt null.Time
		if !goods.Days.IsZero() {
			expiredAt = null.TimeFrom(time.Now().AddDate(0, 0, int(goods.Days.Int64)))
		}

		// 新增會員道具卡
		for i := 0; i < act.FreeGoodsNum; i++ {
			memGoods := MemberGoods{
				MemberID:  mem.ID,
				GoodsID:   null.IntFrom(int64(goods.ID)),
				GoodsType: null.IntFrom(int64(goods.GoodsType)),
				ExpiredAt: expiredAt,
				Status:    "N",
				IsFree:    "Y",
			}

			if err := conn.Create(&memGoods).Error; err != nil {
				return err
			}
		}

		// 新增道具卡紀錄
		if err := conn.Create(&PointHistory{
			MemberID:   mem.ID,
			ActivityID: null.IntFrom(int64(act.ID)),
			Points:     0,
			Reason:     fmt.Sprintf("%s｜贈送道具卡 - %s x %d", utils.RemoveBreakString(act.Name), utils.RemoveBreakString(goods.Name), act.FreeGoodsNum),
		}).Error; err != nil {
			return err
		}
	}

	return nil
}
