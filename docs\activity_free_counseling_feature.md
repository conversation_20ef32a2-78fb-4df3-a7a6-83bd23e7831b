# 活動贈送輔導功能說明

## 功能概述

此功能允許在活動設定中配置贈送輔導，當會員符合活動條件時，系統會自動贈送指定的輔導項目。

## 實現的功能

### 1. UI 介面
- 在活動編輯頁面新增「贈送輔導」設定區塊
- 支援選擇多種輔導類型和數量
- 提供直觀的表格顯示已設定的贈送輔導
- 支援新增、編輯和刪除贈送輔導設定

### 2. 數據結構
- 新增 `activity_free_counselings` 資料表
- 包含欄位：`activity_id`, `counsel_type_id`, `amount`
- 建立適當的外鍵約束

### 3. 後端邏輯
- 在 Activity 模型中新增 FreeCounselingLimit 和 FreeCounselings 欄位
- 實現 UpdateFreeCounselings 方法處理關聯資料
- 在活動觸發時自動建立輔導預約

## 使用方式

### 設定贈送輔導
1. 進入活動編輯頁面
2. 找到「贈送輔導」區塊
3. 勾選「設定贈送輔導」
4. 選擇輔導類型和數量
5. 點擊「新增」按鈕
6. 可重複新增多種輔導類型

### 管理已設定的輔導
- 在表格中可以看到所有已設定的贈送輔導
- 點擊刪除按鈕可移除特定的輔導設定
- 如果新增相同的輔導類型，會更新數量而非重複新增

## 技術實現細節

### 前端 (Vue.js)
```javascript
// 新增的資料欄位
counselTypesList: [], // 所有輔導類型
selectedCounselType: '', // 選擇的輔導類型
selectedCounselAmount: 1, // 選擇的輔導數量

// 主要方法
getCounselTypes() // 獲取輔導類型列表
addFreeCounseling() // 新增贈送輔導
removeFreeCounseling(index) // 移除贈送輔導
getCounselTypeName(counselTypeId) // 獲取輔導類型名稱
```

### 後端 (Go)
```go
// 模型結構
type ActivityFreeCounseling struct {
    ActivityID    uint `form:"activity_id" json:"activity_id"`
    CounselTypeID uint `form:"counsel_type_id" json:"counsel_type_id"`
    Amount        int  `form:"amount" json:"amount"`
}

// 主要方法
func (a *Activity) UpdateFreeCounselings(db *gorm.DB) error
func giveCounselingByActivity(conn *gorm.DB, mem *Member, act *Activity) error
```

## 資料庫遷移

執行以下遷移檔案：
- `000234_create_free_counseling_to_activities.up.mysql`

## 注意事項

1. 輔導類型必須是啟用狀態才會出現在選擇列表中
2. 贈送的輔導會自動建立為免費預約
3. 系統會自動建立相關的輔導作業
4. 每個活動可以設定多種不同的輔導類型
5. 相同輔導類型只能設定一次，重複新增會更新數量

## 測試建議

1. 建立測試活動並設定贈送輔導
2. 確認輔導類型列表正確載入
3. 測試新增、編輯、刪除功能
4. 驗證活動觸發時是否正確建立輔導預約
5. 檢查資料庫中的關聯資料是否正確儲存
