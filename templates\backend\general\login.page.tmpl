<!DOCTYPE html>
<html lang="zh-tw">
    <head>
        {{template "head" .}}
        {{template "validate" .}}
        {{template "sweet_alert" .}}

        <link rel="stylesheet" href="/admin/assets/css/login.min.css">
    </head>
    <body>
        {{template "top" .}}

        {{template "admin/signin" .}}

        {{template "bottom" .}}
    </body>
    
    <script>
        const app = createApp({
            delimiters: ['${', '}'],
            mixins: [ChecksumMixin],
            data() {
                return {
                    msg: location.search.split('msg=')[1] || '',
                    account: {
                        uid: '',
                        pwd: '',
                    },
                }
            },
            mounted() {
                $('#main_form').validate({
                    rules: {
                        checksum: this.checksumRules,
                    },
                });
            },
            methods: {
                signin() {
                    if (!$('#main_form').valid()) {
                        return
                    }

                    msgLoading();
                    
                    axiosRequest()
                    .post('/api/admin/signin', this.account)
                    .then(res => {
                        msgSuccess("登入成功！", res.data.rtn);
                    })
                    .catch(err => {
                        console.log(err);
                        msgError(err.response.data.msg);
                    })
                }
            }
        }).mount('#app')
    </script>
</html>

{{define "admin/signin"}}
    <div id="app" class="content_box">
        <form id="main_form" @submit.prevent="signin">
            <span class="text-danger small" v-if="msg == 'A'">您尚未登錄系統或登錄時間逾時，請重新登錄！</span>

            <div class="text-danger text-subtitle1 fw-bold mb-3">
                網路空間及SSL租用期間即將於10/1號到期！
            </div>

            <label class="d-block mt-2">
                <div class="d-flex">
                    <i class="fas fa-user"></i>
                    <div>使用者帳號</div>
                </div>
                <input name="uid" type="text" v-model="account.uid" class="form-control" maxlength="100" required>
            </label>

            <label class="d-block">
                <div class="d-flex">
                    <i class="fas fa-key"></i>
                    <div>使用者密碼</div>
                </div>
                <input name="pwd" type="password" v-model="account.pwd" class="form-control" maxlength="100" required>
            </label>

            <label class="d-block">
                <div class="d-flex">
                    <i class="fas fa-shield-alt"></i>
                    <div>驗證碼</div>
                </div>
                <div class="d-flex checksum">
                    <div class="col-sm-7">
                        <input type="text" name="checksum" v-model="checksum" class="form-control" maxlength="4" required>
                    </div>
                    <div class="col-sm-5">
                        <button type="button" @click="getChecksum">
                            <img :src="checksumImg" alt="驗證碼">
                            <span>
                                <i class="fas fa-sync-alt"></i>
                            </span>
                        </button>
                    </div>
                </div>
            </label>

            <div class="d-grid gap-1">
                <button type="submit" class="btn btn-lg btn-normal">
                    <i class="fas fa-sign-in-alt"></i>
                    登入
                </button>
            </div>
        </form>
    </div>
{{end}}