<!DOCTYPE html>
<html>
    <head>
        {{template "head" .}}
        {{template "sweet_alert" .}}
        {{template "validate" .}}
        {{template "datepicker" .}}
        {{template "ckeditor" .}}
        {{template "choices" .}}

        {{template "activity.assets" .}}
        <script src="/admin/assets/js/kind_tree.min.js?v=1130"></script>
    </head>
    <body>
        {{template "top" .}}

        {{template "admin.reg" .}}

        {{template "bottom" .}}
    </body>
    <script>
        const app = createApp({
            delimiters: ['${', '}'],
            mixins: [CommonMixin, LevelMixin, Activity, CkEditorMixin, ChoicesMixin, KindTree],
            data() {
                return {
                    t_name: '',
                    item: {
                        id: this.getUrlParam('id' , 0),
                        name: '',   // 活動名稱
                        a_type: 'P',    // 適用類型 P: 購買課程, S: 課費儲值, G: 購買道具卡
                        start_at: '',   // 開始時間
                        end_at: '', // 結束時間

                        trigger_num_limit: 'N', // 活動觸發限制 N: 不設定, Y: 設定
                        trigger_num: 1, // 活動觸發次數

                        rebate_type: 'F',    // 返點類型 F: 固定點數, L: 依等級
                        rebate: '', // 返點
                        rebate_levels: [],  // 返點等級

                        free_pro_limit: 'N',  // 贈送課程 N: 不設定, Y: 設定
                        free_pro_num: 1,    // 贈送課程次數
                        free_pro_id: '',   // 贈送課程ID

                        free_counseling_limit: false,
                        free_counselings: [],

                        free_goods_limit: 'N',  // 贈送道具卡 N: 不設定, Y: 設定
                        free_goods_num: 1,  // 贈送道具卡次數
                        free_goods_id: '',  // 贈送道具卡ID
                        // 適用
                        levels: [],
                        a_pro_kind: 'N',    // 適用課程類別 N:不設定, Y:限制
                        a_pro_kinds: [],    // 適用課程類別
                        a_pro: 'N', // 適用課程 N:不設定, Y:限制
                        a_pros: [],
                        a_pay_types: [], // 適用付款方式

                        a_store_limit: 'N', // 適用儲值 N: 不設定, Y: 限制
                        a_stores: [], // 適用儲值ID

                        a_goods_limit: 'N', // 適用道具卡 N: 不設定, Y: 限制
                        a_goods: [], // 適用道具卡ID

                        a_pro_price_limit: 'N', // 課程金額限制 N: 不設定, Y: 限制
                        a_pro_price: 1, // 課程金額
                        // 條件
                        // product
                        pro_num_limit: 'N', // 課程次數  N: 不設定, Y: 限制
                        pro_num: 1, // 課程次數
                        pro_limit: 'N', // 課程條件  N: 不設定, A: 全部上過, O: 其中
                        pro_limit_num: 1,   // 課程條件數量
                        limit_pros: [], // 課程條件課程
                        // goods
                        goods_num_limit: 'N', // 道具卡購買次數  N: 不設定, Y: 限制
                        goods_num: 1, // 道具卡購買次數
                        note: '',
                        status: 'Y',
                    },
                    stores: [], // 儲值
                    tKindList: [],  // 所有課程類別(未分類)
                    kindList: [],   // 課程類別
                    proList: [],    // 所有課程
                    goodsList: [],  // 所有道具卡
                    freeProSelect: null,    // 選擇贈送課程
                    a_pro_kind_select: '',   // 選擇適用課程類別
                    limit_pro_kind: '', // 選擇課程條件的課程類別
                    start_time: null,
                    end_time: null,
                    created_by: '',
                    updated_by: '',
                }
            },
            async mounted() {
                this.renderDatePicker()
                this.freeProSelect = this.renderChoices('請選擇課程', '[name="free_pro"]')
                this.start_time = this.renderTimePicker('[name="start_time"]')
                this.end_time = this.renderTimePicker('[name="end_time"]')

                $('#main_form').validate()
                
                this.initRebateLevels()
                this.getProKindList()
                await this.getProList()
                this.getStores()
                this.getGoods()
                this.getData()
            },
            methods: {
                getStores() {
                    axiosRequest()
                        .get('/api/admin/points/stored')
                        .then(res => {
                            this.stores = res.data.data
                        })
                        .catch(err => {
                            console.log(err)
                            msgError(err.response.data.msg)
                        })
                },
                getGoods() {
                    axiosRequest()
                        .get('/api/admin/goods')
                        .then(res => {
                            this.goodsList = res.data.data
                        })
                        .catch(err => {
                            console.log(err)
                            msgError(err.response.data.msg)
                        })
                },
                initRebateLevels() {
                    this.Levels.forEach(level => {
                        if (!this.getRebateLevel(level.val)) {
                            this.item.rebate_levels.push({
                                level: level.val,
                                rebate: 0,
                            })
                        }
                    })
                },
                getProKindList() {
                    axiosRequest()
                        .get('/api/admin/products/kind')
                        .then(res => {
                            const data = res.data.data

                            this.tKindList = data
                            this.kindList = this.genKindTree(data)
                        })
                        .catch(err => {
                            console.log(err)
                            console.log(err.response.data)
                            msgError(err.response.data.msg)
                        })
                },
                getProList() {
                    return axiosRequest()
                        .get('/api/admin/products', {
                            params: {
                                status: 'Y',
                                start_at: moment().format('YYYY-MM-DD HH:mm:ss'),
                                end_at: moment().format('YYYY-MM-DD HH:mm:ss'),
                            }
                        })
                        .then(res => {
                            this.proList = res.data.data

                            let data = this.proList.map(item => {
                                return {
                                    value: item.id,
                                    label: this.rmBreakTag(item.pro_name),
                                }
                            })

                            this.setChoices(data, this.freeProSelect)
                        })
                        .catch(err => {
                            console.log(err)
                            console.log(err.response.data)
                            msgError(err.response.data.msg)
                        })
                },
                getData() {
                    if (!this.item.id)
                        return

                    axiosRequest()
                        .get('/api/admin/activity/' + this.item.id)
                        .then(res => {
                            const data = res.data.data

                            this.bindJSON(this.item, data)
                            this.t_name = this.item.name

                            this.setDatePicker('[name="start_date"]', this.item.start_at)
                            this.setTimePicker(this.start_time, this.item.start_at)
                            this.setDatePicker('[name="end_date"]', this.item.end_at)
                            this.setTimePicker(this.end_time, this.item.end_at)

                            this.created_by = `${data.created_by} ${this.formatDateTime(data.created_at, true, '/')}`
                            this.updated_by = `${data.updated_by} ${this.formatDateTime(data.updated_at, true, '/')}`

                            this.initRebateLevels()

                            this.setChoiceValue(this.item.free_pro_id, this.freeProSelect)
                        })
                        .catch(err => {
                            console.log(err)
                            console.log(err.response.data)
                            msgError(err.response.data.msg)
                        })
                },
                submitData() {
                    if (!$('#main_form').valid() || !this.dataValid())
                        return

                    this.handleFormData()

                    if (this.item.id) {
                        this.updateData()
                    }
                    else {
                        this.createData()
                    }
                },
                dataValid() {
                    if (this.item.a_type == 'P') {
                        if (this.item.pro_limit == 'A' || this.item.pro_limit == 'O') {
                            if (this.item.limit_pros.length == 0) {
                                msgError('請選擇課程條件')
                                return false
                            }
    
                            if (this.item.pro_limit == 'O' && this.item.pro_limit_num > this.item.limit_pros.length) {
                                msgError('課程條件數量不可大於選擇的課程數量')
                                return false
                            }
                        }
                    }
                    else if (this.item.a_type == 'S') {
                        if (this.item.free_pro_limit == 'Y') {
                            if (this.item.free_pro_num <= 0) {
                                msgError('請選擇贈送課程次數')
                                return false
                            }

                            if (this.item.free_pro_id == 0) {
                                msgError('請選擇贈送課程')
                                return false
                            }
                        }

                        if (this.item.a_pay_types.length == 0) {
                            msgError('請選擇適用付款方式')
                            return false
                        }

                        if (this.item.a_store_limit == 'Y' && this.item.a_stores.length == 0) {
                            msgError('請選擇適用儲值')
                            return false
                        }
                    }
                    else if (this.item.a_type == 'G') {
                        if (this.isZeroDate($('[name="start_date"]').val())) {
                            msgError('請選擇開放時間')
                            return false
                        }
                    }

                    return true
                },
                createData() {
                    msgLoading()

                    axiosRequest('json')
                        .post('/api/admin/activity', this.item)
                        .then(res => {
                            msgSuccess(res.data.msg, '/admin/activity/index')
                        })
                        .catch(err => {
                            console.log(err)
                            console.log(err.response.data)
                            msgError(err.response.data.msg)
                        })
                },
                updateData() {
                    msgLoading()

                    axiosRequest('json')
                        .patch('/api/admin/activity/' + this.item.id, this.item)
                        .then(res => {
                            msgSuccess(res.data.msg, '/admin/activity/index')
                        })
                        .catch(err => {
                            console.log(err)
                            console.log(err.response.data)
                            msgError(err.response.data.msg)
                        })
                },
                handleFormData() {
                    this.item.start_at = this.getDateTimePicker('[name="start_date"]', '[name="start_time"]', 1)
                    this.item.start_at = this.formatFullDate(this.item.start_at)
                    this.item.end_at = this.getDateTimePicker('[name="end_date"]', '[name="end_time"]', 2)
                    this.item.end_at = this.formatFullDate(this.item.end_at)

                    if (this.item.rebate === '') {
                        this.item.rebate = 0
                    }

                    this.item.rebate_levels.forEach(item => {
                        if (item.rebate === '') {
                            item.rebate = 0
                        }
                    })

                    if (!this.item.free_pro_id) {
                        this.item.free_pro_id = null
                    }

                    if (!this.item.free_goods_id) {
                        this.item.free_goods_id = null
                    }

                    this.item.note = this.CkEditorGetData('note')
                },
                deleteData() {
                    msgConfirm(`確認要刪除<font color=red>【${this.t_name}】</font>的活動嗎`, 
                    () => {
                        msgLoading()

                        axiosRequest()
                            .delete('/api/admin/activity/' + this.item.id)
                            .then(res => {
                                msgSuccess(res.data.msg, '/admin/activity/index')
                            })
                            .catch(err => {
                                console.log(err)
                                console.log(err.response.data)
                                msgError(err.response.data.msg)
                            })
                    })
                },
                getRebateLevel(level) {
                    return this.item.rebate_levels.find(item => item.level == level)
                },
                onSelectAProKind(kind_id) {
                    if (!kind_id)
                        return

                    let kind = this.item.a_pro_kinds.find(item => item.kind_id == kind_id)
                    
                    if (kind) {
                        this.item.a_pro_kinds = this.item.a_pro_kinds.filter(item => item.kind_id != kind_id)
                    }
                    else {
                        this.item.a_pro_kinds.push({
                            activity_id: this.item.id,
                            kind_id: kind_id,
                        })
                    }
                },
                getAProKind(kind_id) {
                    return this.item.a_pro_kinds.find(item => item.kind_id == kind_id)
                },
                onSelectAPro(pro_id) {
                    if (!pro_id)
                        return

                    let pro = this.item.a_pros.find(item => item.pro_id == pro_id)

                    if (pro) {
                        this.item.a_pros = this.item.a_pros.filter(item => item.pro_id != pro_id)
                    }
                    else {
                        this.item.a_pros.push({
                            activity_id: this.item.id,
                            pro_id: pro_id,
                        })
                    }
                },
                getAPro(pro_id) {
                    return this.item.a_pros.find(item => item.pro_id == pro_id)
                },
                onSelectLimitPro(pro_id) {
                    if (!pro_id)
                        return

                    let pro = this.item.limit_pros.find(item => item.pro_id == pro_id)

                    if (pro) {
                        this.item.limit_pros = this.item.limit_pros.filter(item => item.pro_id != pro_id)
                    }
                    else {
                        this.item.limit_pros.push({
                            activity_id: this.item.id,
                            pro_id: pro_id,
                        })
                    }
                },
                getLimitPro(pro_id) {
                    return this.item.limit_pros.find(item => item.pro_id == pro_id)
                },
                onSelectAPayType(payType) {
                    let pay = this.item.a_pay_types.find(item => item.pay_type == payType)
                    
                    if (pay) {
                        this.item.a_pay_types = this.item.a_pay_types.filter(item => item.pay_type != payType)
                        console.log(this.item.a_pay_types);
                    }
                    else {
                        this.item.a_pay_types.push({
                            activity_id: this.item.id,
                            pay_type: payType,
                        })
                    }
                },
                isAPayTypeSelected(payType) {
                    return this.item.a_pay_types.find(item => item.pay_type == payType)
                },
                onSelectAStore(storeID) {
                    let store = this.item.a_stores.find(item => item.store_id == storeID)

                    if (store) {
                        this.item.a_stores = this.item.a_stores.filter(item => item.store_id != storeID)
                    }
                    else {
                        this.item.a_stores.push({
                            activity_id: this.item.id,
                            store_id: storeID,
                        })
                    }
                },
                isAStoreSelected(storeID) {
                    return this.item.a_stores.find(item => item.store_id == storeID)
                },
                onSelectAGoods(goodsID) {
                    let goods = this.item.a_goods.find(item => item.goods_id == goodsID)

                    if (goods) {
                        this.item.a_goods = this.item.a_goods.filter(item => item.goods_id != goodsID)
                    }
                    else {
                        this.item.a_goods.push({
                            activity_id: this.item.id,
                            goods_id: goodsID,
                        })
                    }
                },
                isAGoodsSelected(goodsID) {
                    return this.item.a_goods.find(item => item.goods_id == goodsID)
                },
                goBack() {
                    window.location.href = '/admin/activity/index'
                }
            },
            computed: {
                aProList() {
                    if (this.a_pro_kind_select == '')
                        return []

                    return this.proList.filter(item => {
                        return (
                            item.pro_kind_id == this.a_pro_kind_select
                        )
                    })
                },
                limitProList() {
                    if (this.limit_pro_kind == '')
                        return []

                    return this.proList.filter(item => {
                                return (
                                    item.pro_kind_id == this.limit_pro_kind
                                )
                            })
                },
                isProAvailable() {
                    return this.item.a_type === 'P'
                },
                isStoreAvailable() {
                    return this.item.a_type === 'S'
                },
                isGoodsAvailable() {
                    return this.item.a_type === 'G'
                },
                validGoodsList() {
                    // 適用道具卡，未過期且狀態為Y
                    return this.goodsList.filter(goods => goods.status == 'Y' && !this.isDateBefore(goods.end_at))
                }
            }
        }).mount('#app')
    </script>
</html>

{{define "admin.reg"}}
    <div id="app" class="content_box">
        <form id="main_form" @submit.prevent="submitData" class="card">
            <div class="card-header">以下 * 欄位為必填欄位</div>
            <div class="card-body">
                <!-- 活動名稱 -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-md-2 col-form-label text-md-end">* 活動名稱</label>
                    <div class="col-sm-9 col-md-10">
                        <input type="text" name="name" v-model="item.name" class="form-control" required>
                    </div>
                </div>
                <!-- 適用類型 -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-md-2 col-form-label text-md-end">* 適用類型</label>
                    <div class="col-sm-9 col-md-10">
                        <select name="a_type" v-model="item.a_type" class="form-select" required>
                            <option v-for="t in ATypes" :value="t.value" v-html="t.label"></option>
                        </select>
                    </div>
                </div>
                <!-- 適用等級 -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-md-2 col-form-label text-md-end">* 適用等級</label>
                    <div class="col-sm-9 col-md-10 align-self-center">
                        <div class="row">
                            <label v-for="level in Levels" class="col-auto ">
                                <input type="checkbox" name="levels" v-model="item.levels" :value="level.val" class="form-check-input me-1" required />
                                <span v-html="level.txt"></span>
                            </label>
                        </div>
                    </div>
                </div>
                <!-- 開放時間 -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-md-2 col-form-label text-md-end">開放時間</label>
                    <div class="col-sm-9 col-md-10 d-flex flex-wrap">
                        <input type="text" name="start_date" class="form-control datepicker w-auto me-1 mb-1">
                        <input type="text" name="start_time" class="form-control datetimepicker w-auto mb-1">
                        <span class="input-group-text mx-1 mb-1">~</span>
                        <input type="text" name="end_date" class="form-control datepicker me-1 w-auto mb-1">
                        <input type="text" name="end_time" class="form-control datetimepicker w-auto mb-1">
                    </div>
                </div>

                <!-- 觸發次數限制 -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-md-2 col-form-label text-md-end">* 活動領取次數限制</label>
                    <div class="col-sm-9 col-md-10">
                        <div class="d-flex align-items-center">
                            <label class="form-check form-check-inline">
                                <input type="radio" name="trigger_num_limit" value="N" v-model="item.trigger_num_limit" class="form-check-input" required>
                                <span class="user-select-none">不設定</span>
                            </label>
                            <label class="form-check form-check-inline d-flex align-items-center">
                                <input type="radio" name="trigger_num_limit" value="Y" v-model="item.trigger_num_limit" class="form-check-input">
                                <span class="user-select-none ms-2">每人限領</span>
                                <select name="trigger_num" v-model.number="item.trigger_num" class="form-select w-auto mx-2" :required="item.trigger_num_limit == 'Y'">
                                    <option v-for="i in 99" :value="i">${i}</option>
                                </select>
                                <span class="user-select-none">次</span>
                            </label>
                        </div>
                        <div v-if="isGoodsAvailable && item.trigger_num_limit=='Y'">
                            <span class="form-control-plaintext fw-bold">(不同道具卡的次數個別計算)</span>
                        </div>
                    </div>
                </div>

                <!-- 返點 -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-md-2 col-form-label text-md-end">* 贈送點數</label>
                    <div class="col-sm-9 col-md-10">
                        <div class="align-self-center mb-1">
                            <label class="form-check-inline me-3">
                                <input type="radio" name="rebate_type" value="F" v-model="item.rebate_type" class="form-check-input me-1" required>
                                <span class="user-select-none">固定點數</span>
                            </label>
                            <label class="form-check-inline">
                                <input type="radio" name="rebate_type" value="L" v-model="item.rebate_type" class="form-check-input me-1">
                                <span class="user-select-none">依等級</span>
                            </label>
                        </div>
                        <div>
                            <div v-show="item.rebate_type == 'F'">
                                <input type="number" name="rebate" v-model="item.rebate" class="form-control" pattern="[0-9]*" required>
                            </div>
                            <div v-show="item.rebate_type == 'L'">
                                <div class="d-inline-flex flex-wrap">
                                    <template v-for="level in Levels">
                                        <div class="border p-2 me-2" v-if="getRebateLevel(level.val)">
                                            <span class="fw-bold me-1" v-html="level.txt"></span>
                                            <input name="rebate_level" type="number" v-model="getRebateLevel(level.val).rebate" 
                                                :data-level="level.val" class="form-control w-auto" max="9999999" :required="item.rebate_type == 'L'"
                                            >
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 贈送課程 -->
                <div class="row mb-3" v-show="isStoreAvailable">
                    <label class="col-sm-3 col-md-2 col-form-label text-md-end">* 贈送課程</label>
                    <div class="col-sm-9 col-md-10 d-flex align-items-center">
                        <label class="form-check form-check-inline">
                            <input type="radio" name="free_pro_limit" value="N" v-model="item.free_pro_limit" class="form-check-input" required>
                            <span class="user-select-none">不設定</span>
                        </label>
                        <label class="form-check form-check-inline d-flex align-items-center">
                            <input type="radio" name="free_pro_limit" value="Y" v-model="item.free_pro_limit" class="form-check-inline">
                            <span class="user-select-none" v-if="item.free_pro_limit == 'N'">設定</span>
                            <template v-else>
                                <span class="user-select-none text-nowrap me-2">贈送</span>
                                <select name="free_pro_num" v-model.number="item.free_pro_num" class="form-select w-auto" required>
                                    <option v-for="i in 99" :value="i">${i}</option>
                                </select>
                                <span class="user-select-none mx-2">堂</span>
                            </template>
                            <span v-show="item.free_pro_limit == 'Y'">
                                <select name="free_pro" v-model.number="item.free_pro_id" class="form-select js-choice"></select>
                            </span>
                        </label>
                    </div>
                </div>
                <!-- 贈送道具 -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-md-2 col-form-label text-end">* 贈送道具卡</label>
                    <div class="col-sm-9 col-md-10 d-flex align-items-center">
                        <label class="form-check form-check-inline">
                            <input type="radio" name="free_goods_limit" value="N" v-model="item.free_goods_limit" class="form-check-input" required>
                            <span class="user-select-none">不設定</span>
                        </label>
                        <label class="form-check form-check-inline d-flex align-items-center">
                            <input type="radio" name="free_goods_limit" value="Y" v-model="item.free_goods_limit" class="form-check-inline">
                            <span class="user-select-none" v-if="item.free_goods_limit == 'N'">設定</span>
                            <template v-else>
                                <span class="user-select-none text-nowrap me-2">贈送</span>
                                <select name="free_goods_num" v-model.number="item.free_goods_num" class="form-select w-auto" required>
                                    <option v-for="i in 99" :value="i">${i}</option>
                                </select>
                                <span class="user-select-none mx-2">張</span>
                                <span v-if="isGoodsAvailable">相同道具卡</span>
                                <select name="free_goods" v-model.number="item.free_goods_id" class="form-select w-auto" required v-else>
                                    <option value="" disabled>請選擇道具卡</option>
                                    <option v-for="goods of goodsList" :value="goods.id" v-html="goods.name"></option>
                                </select>
                            </template>
                        </label>
                    </div>
                </div>

                <!-- 說明 -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-md-2 col-form-label text-md-end">說明</label>
                    <div class="col-sm-9 col-md-10">
                        <textarea name="note" v-model="item.note" class="form-control ckeditor" rows="5"></textarea>
                    </div>
                </div>
            </div>

            <!-- 適用範圍 -->
            <div class="card-header">適用範圍</div>
            <div class="card-body">
                <!-- 購買課程 -->
                <template v-if="isProAvailable">
                    <span class="form-control-plaintext fw-bold user-select-none col-sm-10 offset-sm-2">
                        ※「適用課程類別」和「適用課程」皆設為「限制」時，只要符合其中一項即可。
                    </span>
                    <!-- 適用課程類別 -->
                    <div class="row mb-3">
                        <label class="col-sm-4 col-md-2 col-form-label text-md-end">* 適用課程類別</label>
                        <div class="col-sm-9 col-md-10 align-self-center">
                            <label class="form-check form-check-inline">
                                <input type="radio" name="a_pro_kind" v-model="item.a_pro_kind" value="N" class="form-check-input" checked required>
                                <span class="user-select-none">不設定</span>
                            </label>
                            <label class="form-check form-check-inline">
                                <input type="radio" name="a_pro_kind" v-model="item.a_pro_kind" value="Y" class="form-check-input" required>
                                <span class="user-select-none">限制</span>
                            </label>
                            <template v-if="item.a_pro_kind == 'Y'">
                                <div class="form-control select-area">
                                    <span>
                                        <template v-for="kind in getKindList(kindList)">
                                            <template v-if="!getAProKind(kind.id)">
                                                <label>
                                                    <input type="checkbox" @click="onSelectAProKind(kind.id)">
                                                    <span v-html="kind.kind_name"></span>
                                                </label>
                                                <template v-for="child in kind.children">
                                                    <label v-if="!getAProKind(child.id)">
                                                        <input type="checkbox" @click="onSelectAProKind(child.id)">
                                                        <span v-html="`...${child.kind_name}`"></span>
                                                    </label>
                                                </template>
                                            </template>
                                        </template>
                                    </span>
                                </div>
                                <div>
                                    <span v-if="item.a_pro_kinds.length == 0">目前尚未選擇課程類別</span>
                                    <template v-else>
                                        <span>已選取的課程類別：<br></span>
                                        <div>
                                            <span v-for="(k, index) in item.a_pro_kinds" @click="onSelectAProKind(k.kind_id)">
                                                <input type="checkbox" class="form-check-input pe-none" checked disabled>
                                                <span class="user-select-none">${ tKindList.find(item => item.id == k.kind_id)?.kind_name }</span>
                                                <span v-if="index != item.a_pro_kinds.length - 1">,</span>
                                            </span>
                                        </div>
                                    </template>
                                </div>
                            </template>
                        </div>
                    </div>
                    <!-- 適用課程 -->
                    <div class="row mb-3">
                        <label class="col-sm-4 col-md-2 col-form-label text-md-end">* 適用課程</label>
                        <div class="col-sm-10 align-self-center">
                            <label class="form-check form-check-inline">
                                <input type="radio" name="a_pro" value="N" v-model="item.a_pro" class="form-check-input" checked required>
                                <span class="user-select-none">不設定</span>
                            </label>
                            <label class="form-check form-check-inline">
                                <input type="radio" name="a_pro" value="Y" v-model="item.a_pro" class="form-check-input" required>
                                <span class="user-select-none">限制</span>
                            </label>
                            <template v-if="item.a_pro == 'Y'">
                                <select name="find_kind" class="form-control form-select" v-model="a_pro_kind_select">
                                    <option value="">請選擇課程類別</option>
                                    <option v-for="kind in getKindList(kindList)" :value="kind.id" v-html="kind.kind_name"></option>
                                </select>
                                <div class="form-control select-area">
                                    <span>
                                        <template v-for="pro in aProList">
                                            <label v-if="!getAPro(pro.id)">
                                                <input type="checkbox" @click="onSelectAPro(pro.id)">
                                                <span v-html="pro.pro_name"></span>
                                            </label>
                                        </template>
                                    </span>
                                </div>
                                <div>
                                    <span v-if="item.a_pros.length == 0">目前尚未選擇課程</span>
                                    <template v-else>
                                        <span>已選取的課程：<br></span>
                                        <div>
                                            <span v-for="(p, index) in item.a_pros" @click="onSelectAPro(p.pro_id)" class="d-inline-flex">
                                                <input type="checkbox" class="form-check-input pe-none" checked disabled>
                                                <span class="user-select-none" v-html="proList.find(item => item.id == p.pro_id)?.pro_name"></span>
                                                <span v-if="index != item.a_pros.length - 1">,</span>
                                            </span>
                                        </div>
                                    </template>
                                </div>
                            </template>
                        </div>
                    </div>
                </template>

                <!-- 儲值課費 -->
                <template v-else-if="isStoreAvailable">
                    <!-- 適用付款方式 -->
                    <div class="row mb-3">
                        <label class="col-sm-3 col-md-2 col-form-label text-md-end">* 適用付款方式</label>
                        <div class="col-sm-9 col-md-10 align-self-center">
                            <label class="form-check form-check-inline">
                                <input type="checkbox" name="a_pay_type" value="atm" @click="onSelectAPayType('atm')" :checked="isAPayTypeSelected('atm')" class="form-check-input me-2">
                                <span class="user-select-none">ATM轉帳</span>
                            </label>
                            <label class="form-check form-check-inline">
                                <input type="checkbox" name="a_pay_type" value="newebpay" @click="onSelectAPayType('newebpay')" :checked="isAPayTypeSelected('newebpay')" class="form-check-input me-2">
                                <span class="user-select-none">線上刷卡</span>
                            </label>
                            <label class="form-check form-check-inline">
                                <input type="checkbox" name="a_pay_type" value="zingala" @click="onSelectAPayType('zingala')" :checked="isAPayTypeSelected('zingala')" class="form-check-input me-2">
                                <span class="user-select-none">無卡分期</span>
                            </label>
                        </div>
                    </div>

                    <!-- 適用儲值金額 -->
                    <div class="row mb-3" v-if="isStoreAvailable">
                        <label class="col-sm-3 col-md-2 col-form-label text-md-end">
                            * 適用儲值
                        </label>
                        <div class="col-sm-9 col-md-10 align-self-center">
                            <label class="form-check form-check-inline">
                                <input type="radio" name="a_store_limit" v-model="item.a_store_limit" value="N" class="form-check-input">
                                <span class="user-select-none">不限制</span>
                            </label>
                            <label class="form-check form-check-inline">
                                <input type="radio" name="a_store_limit" v-model="item.a_store_limit" value="Y" class="form-check-input">
                                <span class="user-select-none" v-if="item.a_type == 'P' && item.a_store_limit == 'Y'">需完成其中一種儲值</span>
                                <span class="user-select-none" v-else>限制</span>
                            </label>
                            <div v-if="item.a_store_limit=='Y'" class="mt-3">
                                <label class="form-check form-check-inline" v-for="store in stores">
                                    <input type="checkbox" name="a_store" :value="store.id" @click="onSelectAStore(store.id)" :checked="isAStoreSelected(store.id)" class="form-check-input me-2">
                                    <span class="user-select-none">
                                        儲值$${ addCommas(store.point) }
                                    </span>
                                </label>
                            </div>
                        </div>
                    </div>
                </template>

                <!-- 購買道具 -->
                <template v-if="isGoodsAvailable">
                    <!-- 適用道具 -->
                    <div class="row mb-3">
                        <label class="col-sm-3 col-md-2 col-form-label text-md-end">* 適用道具卡</label>
                        <div class="col-sm-9 col-md-10 align-self-center">
                            <div class="d-flex align-self-center">
                                <label class="form-check form-check-inline">
                                    <input type="radio" name="a_goods_limit" v-model="item.a_goods_limit" value="N" class="form-check-input">
                                    <span class="user-select-none">不限制</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <input type="radio" name="a_goods_limit" v-model="item.a_goods_limit" value="Y" class="form-check-input">
                                    <span class="user-select-none">限制</span>
                                </label>
                            </div>
                            <template v-if="item.a_goods_limit == 'Y'">
                                <div class="form-control select-area">
                                    <span>
                                        <template v-for="goods of goodsList">
                                            <label v-if="!isAGoodsSelected(goods.id)">
                                                <input type="checkbox" @click="onSelectAGoods(goods.id)">
                                                <span v-html="goods.name"></span>
                                            </label>
                                        </template>
                                    </span>
                                </div>
                                <div>
                                    <span v-if="item.a_goods.length == 0">目前尚未選擇道具卡</span>
                                    <template v-else>
                                        <span>已選取的道具卡：<br></span>
                                        <div>
                                            <span v-for="(g, index) in item.a_goods" @click="onSelectAGoods(g.goods_id)" class="d-inline-flex">
                                                <input type="checkbox" class="form-check-input pe-none" checked disabled />
                                                <span class="user-select-none" v-html="goodsList.find(item => item.id == g.goods_id)?.name"></span>
                                                <span v-if="index != item.a_goods.length - 1">,</span>
                                            </span>
                                        </div>
                                    </template>
                                </div>
                            </template>
                        </div>
                    </div>
                </template>
            </div>

            <div class="card-header" v-if="isProAvailable || isGoodsAvailable">
                限制條件
            </div>
            <div class="card-body">
                <template v-if="isProAvailable">
                    <!-- 課程金額限制 -->
                    <div class="row mb-3">
                        <label class="col-sm-3 col-md-2 col-form-label text-md-end">* 課程金額限制</label>
                        <div class="col-sm-9 col-md-10">
                            <div class="d-flex align-items-center">
                                <label class="form-check form-check-inline">
                                    <input type="radio" name="a_pro_price_limit" value="N" v-model="item.a_pro_price_limit" class="form-check-input" required>
                                    <span class="user-select-none">不設定</span>
                                </label>
                                <label class="form-check form-check-inline d-flex align-items-center">
                                    <input type="radio" name="a_pro_price_limit" value="Y" v-model="item.a_pro_price_limit" class="form-check-input">
                                    <span class="user-select-none ms-2">課程金額需高於</span>
                                    <input type="number" name="a_pro_price" v-model.number="item.a_pro_price" class="form-control w-auto mx-2" min="1" max="9999999" :required="item.a_pro_price_limit == 'Y'">
                                    <span class="user-select-none">元(含)，才能觸發活動</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 課程下單數量 -->
                    <div class="row mb-3">
                        <label class="col-sm-3 col-md-2 col-form-label text-md-end">* 課程下單數量</label>
                        <div class="col-sm-9 col-md-10">
                            <div class="d-flex align-items-center">
                                <label class="form-check form-check-inline">
                                    <input type="radio" name="pro_num" v-model="item.pro_num_limit" value="N" class="form-check-input" required>
                                    <span class="user-select-none">不設定</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <div class="d-flex align-items-center">
                                        <input type="radio" name="pro_num" v-model="item.pro_num_limit" value="Y" class="form-check-input">
                                        <span class="user-select-none text-nowrap ms-2">需下單</span>
                                        <select name="pro_num" v-model.number="item.pro_num" class="form-select w-auto mx-2" :required="item.pro_num_limit == 'Y'">
                                            <option v-for="i in 99" :value="i">${i}</option>
                                        </select>
                                        <span class="user-select-none text-nowrap">堂課程，才能觸發活動</span>
                                    </div>
                                </label>
                            </div>
                            <div class="form-control-plaintext fw-bold user-select-none">(僅計算活動期間內的下單課程)</div>
                        </div>
                    </div>

                    <!-- 上過課程條件 -->
                    <div class="row mb-3">
                        <label class="col-sm-4 col-md-2 col-form-label text-md-end">* 是否要先上過指定課程</label>
                        <div class="col-sm-10 align-self-center">
                            <div class="d-inline-flex align-items-center mb-2">
                                <label class="form-check-inline">
                                    <input type="radio" name="limit" value="N" v-model="item.pro_limit" class="form-check-input" checked required>
                                    <span class="ms-1">不設定</span>
                                </label>
                                <label class="form-check-inline">
                                    <input type="radio" name="limit" value="A" v-model="item.pro_limit" class="form-check-input" required>
                                    <span class="ms-1">全部上過</span>
                                </label>
                                <label class="form-check-inline d-inline-flex align-items-center">
                                    <input type="radio" name="limit" value="O" v-model="item.pro_limit" class="form-check-input" required>
                                    <span class="d-inline-flex align-items-center ms-1">
                                        其中
                                        <select name="pro_limit_num" v-model="item.pro_limit_num" class="form-control form-select w-auto" :required="item.pro_limit == 'O'">
                                            <option v-for="i in 99" :value="i">${i}</option>                                        
                                        </select>
                                        種上過
                                    </span>
                                </label>
                            </div>
                            <template v-if="item.pro_limit != 'N'">
                                <select name="find_kind" class="form-control form-select" v-model="limit_pro_kind">
                                    <option value="" selected>請選擇課程類別</option>
                                    <option v-for="kind in getKindList(kindList)" :value="kind.id" v-html="kind.kind_name"></option>
                                </select>
                                <div class="form-control select-area">
                                    <span>
                                        <template v-for="pro in limitProList">
                                            <label v-if="!getLimitPro(pro.id)">
                                                <input type="checkbox" @click="onSelectLimitPro(pro.id)">
                                                <span v-html="pro.pro_name"></span>
                                            </label>
                                        </template>
                                    </span>
                                </div>
                                <div>
                                    <span v-if="item.limit_pros.length == 0">目前尚未選擇課程</span>
                                    <template v-else>
                                        <span>已選取的課程：<br></span>
                                        <div>
                                            <span v-for="(p, index) in item.limit_pros" @click="onSelectLimitPro(p.pro_id)" class="d-inline-flex">
                                                <input type="checkbox" class="form-check-input pe-none" checked disabled>
                                                <span class="user-select-none" v-html="proList.find(item => item.id == p.pro_id)?.pro_name"></span>
                                                <span v-if="index != item.limit_pros.length - 1">,</span>
                                            </span>
                                        </div>
                                    </template>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- 是否要在活動期間完成儲值 -->
                    <div class="row mb-3">
                        <label class="col-sm-3 col-md-2 col-form-label text-md-end">
                            * 是否要在活動期間完成儲值
                        </label>
                        <div class="col-sm-9 col-md-10 align-self-center">
                            <label class="form-check form-check-inline">
                                <input type="radio" name="a_store_limit" v-model="item.a_store_limit" value="N" class="form-check-input">
                                <span class="user-select-none">不限制</span>
                            </label>
                            <label class="form-check form-check-inline">
                                <input type="radio" name="a_store_limit" v-model="item.a_store_limit" value="Y" class="form-check-input">
                                <span class="user-select-none" v-if="item.a_type == 'P' && item.a_store_limit == 'Y'">需完成其中一種儲值</span>
                                <span class="user-select-none" v-else>限制</span>
                            </label>
                            <div v-if="item.a_store_limit=='Y'" class="mt-3">
                                <label class="form-check form-check-inline" v-for="store in stores">
                                    <input type="checkbox" name="a_store" :value="store.id" @click="onSelectAStore(store.id)" :checked="isAStoreSelected(store.id)" class="form-check-input me-2">
                                    <span class="user-select-none">
                                        儲值$${ addCommas(store.point) }
                                    </span>
                                </label>
                            </div>
                        </div>
                    </div>
                </template>
                

                <!-- 道具條件 -->
                <template v-if="isGoodsAvailable">
                    <!-- 道具下單數量 -->
                    <div class="row mb-3">
                        <label class="col-sm-3 col-md-2 col-form-label text-md-end">* 道具卡下單數量</label>
                        <div class="col-sm-9 col-md-10">
                            <div class="d-flex align-items-center">
                                <label class="form-check form-check-inline">
                                    <input type="radio" name="goods_num_limit" v-model="item.goods_num_limit" value="N" class="form-check-input" required>
                                    <span class="user-select-none">不限制</span>
                                </label>
                                <label class="form-check form-check-inline d-flex align-items-center">
                                    <input type="radio" name="goods_num_limit" v-model="item.goods_num_limit" value="Y" class="form-check-input" required>
                                    <span class="user-select-none ms-2" v-if="item.goods_num_limit == 'N'">限制</span>
                                    <template v-else>
                                        <span class="user-select-none ms-2">需下單</span>
                                        <select name="goods_num" v-model.number="item.goods_num" class="form-select w-auto mx-2" :required="item.goods_num_limit == 'Y'">
                                            <option v-for="i in 99" :value="i" v-html="i"></option>
                                        </select>
                                        <span class="user-select-none">張</span>
                                    </template>
                                </label>
                            </div>
                            <div class="form-control-plaintext fw-bold user-select-none">(僅計算活動期間內下單的相同道具卡)</div>
                        </div>
                    </div>
                </template>
            </div>

            <div class="card-body">
                <!-- 狀態 -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-md-2 col-form-label text-md-end">* 狀態</label>
                    <div class="col-sm-9 col-md-10 align-self-center">
                        <label class="form-check form-check-inline">
                            <input type="radio" name="status" v-model="item.status" value="Y" class="form-check-input" required>
                            啟用
                        </label>
                        <label class="form-check-inline">
                            <input type="radio" name="status" v-model="item.status" value="N" class="form-check-input" required>
                            停用
                        </label>
                    </div>
                </div>

                <template v-if="item.id">
                    <div class="row mb-3">
                        <label class="col-sm-3 col-md-2 col-form-label text-md-end">建立時間</label>
                        <div class="col-sm-9 col-md-10">
                            <span class="form-control-plaintext">${ created_by }</span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-sm-3 col-md-2 col-form-label text-md-end">修改時間</label>
                        <div class="col-sm-9 col-md-10">
                            <span class="form-control-plaintext">${ updated_by }</span>
                        </div>
                    </div>
                </template>

                <div class="row mb-3">
                    <label class="col-sm-3 col-md-2 col-form-label"></label>
                    <div class="col-sm-9 col-md-10">
                        <button type="submit" class="btn btn-success me-3">送出</button>
                        <button type="button" @click="deleteData" class="btn btn-danger" v-if="item.id">刪除</button>
                        <button type="button" @click="goBack" class="btn btn-light ms-4">回列表</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
{{end}}